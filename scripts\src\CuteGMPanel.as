package src
{
   import com.hotpoint.braveManIII.Tool.XMLAsset;
   import com.hotpoint.braveManIII.models.common.VT;
   import com.hotpoint.braveManIII.repository.achievement.*;
   import com.hotpoint.braveManIII.repository.equip.EquipFactory;
   import com.hotpoint.braveManIII.views.cardPanel.CardPanel;
   import com.hotpoint.braveManIII.views.itemsPanel.JinHuaPanel;
   import com.hotpoint.braveManIII.views.itemsPanel.JinHuaPanel2;
   import com.hotpoint.braveManIII.views.storagePanel.StoragePanel;
   import com.hotpoint.braveManIII.views.youlingPanel.Panel_youling;
   import flash.display.*;
   import flash.events.*;
   import flash.filters.*;
   import flash.geom.*;
   import flash.text.*;
   import flash.utils.Timer;
   import src._data.*;
   import src.tool.TiaoShi;
   
   public class CuteGMPanel extends MovieClip
   {
      private static var instance:CuteGMPanel;
      
      private static const PANEL_WIDTH:Number = 800;
      
      private static const PANEL_HEIGHT:Number = 600;
      
      private static const TAB_HEIGHT:Number = 50;
      
      private static const CONTENT_HEIGHT:Number = PANEL_HEIGHT - TAB_HEIGHT - 60;
      
      private static const PINK_LIGHT:uint = 16758465;
      
      private static const PINK_MEDIUM:uint = 16761035;
      
      private static const PINK_DARK:uint = 16738740;
      
      private static const WHITE:uint = 16777215;
      
      private static const PURPLE_LIGHT:uint = 15132410;
      
      private var mainPanel:Sprite;
      
      private var titleBar:Sprite;
      
      private var tabContainer:Sprite;
      
      private var contentContainer:Sprite;
      
      private var statusBar:Sprite;
      
      private var tabs:Array = [];
      
      private var tabContents:Array = [];
      
      private var currentTab:int = 0;
      
      private var tabData:Array = [{
         "name":"🎮角色",
         "icon":"🎮"
      },{
         "name":"⚔️装备",
         "icon":"⚔️"
      },{
         "name":"🐾宠物",
         "icon":"🐾"
      },{
         "name":"💎道具",
         "icon":"💎"
      },{
         "name":"🎯一键",
         "icon":"🎯"
      },{
         "name":"🔧工具",
         "icon":"🔧"
      }];
      
      private var isVisible:Boolean = false;
      
      private var isDragging:Boolean = false;
      
      private var dragOffset:Point;
      
      public function CuteGMPanel()
      {
         super();
         trace("=== CuteGMPanel构造函数开始执行 ===");
         TiaoShi.txtShow("=== CuteGMPanel构造函数开始执行 ===");

         try
         {
            this.initializePanel();
            trace("CuteGMPanel初始化完成");
            TiaoShi.txtShow("CuteGMPanel初始化完成");
         }
         catch(error:Error)
         {
            trace("CuteGMPanel初始化失败: " + error.message);
            TiaoShi.txtShow("CuteGMPanel初始化失败: " + error.message);
            throw error;
         }
      }
      
      public static function getInstance() : CuteGMPanel
      {
         trace("=== CuteGMPanel.getInstance() 开始执行 ===");

         if(!instance)
         {
            trace("实例不存在，创建新的CuteGMPanel实例");
            TiaoShi.txtShow("实例不存在，创建新的CuteGMPanel实例");

            try
            {
               instance = new CuteGMPanel();
               trace("CuteGMPanel实例创建成功");
               TiaoShi.txtShow("CuteGMPanel实例创建成功");
            }
            catch(error:Error)
            {
               trace("创建CuteGMPanel实例失败: " + error.message);
               TiaoShi.txtShow("创建CuteGMPanel实例失败: " + error.message);
               throw error;
            }
         }
         else
         {
            trace("使用现有的CuteGMPanel实例");
         }

         return instance;
      }
      
      public static function show() : void
      {
         trace("=== CuteGMPanel.show() 开始执行 ===");
         TiaoShi.txtShow("=== CuteGMPanel.show() 开始执行 ===");

         try
         {
            var panel:CuteGMPanel = getInstance();
            trace("Main._stage是否存在: " + (Main._stage != null));
            trace("面板当前可见状态: " + panel.isVisible);
            TiaoShi.txtShow("Main._stage是否存在: " + (Main._stage != null));
            TiaoShi.txtShow("面板当前可见状态: " + panel.isVisible);

            if(!panel.isVisible)
            {
               trace("准备将面板添加到舞台");
               TiaoShi.txtShow("准备将面板添加到舞台");

               Main._stage.addChild(panel);
               panel.isVisible = true;

               trace("面板已添加到舞台，开始播放显示动画");
               TiaoShi.txtShow("面板已添加到舞台，开始播放显示动画");

               panel.playShowAnimation();

               trace("GM面板显示成功！");
               TiaoShi.txtShow("GM面板显示成功！");
            }
            else
            {
               trace("面板已经可见，无需重复显示");
               TiaoShi.txtShow("面板已经可见，无需重复显示");
            }
         }
         catch(error:Error)
         {
            trace("显示GM面板时出错: " + error.message);
            TiaoShi.txtShow("显示GM面板时出错: " + error.message);
         }
      }

      public static function hide() : void
      {
         trace("=== CuteGMPanel.hide() 开始执行 ===");
         TiaoShi.txtShow("=== CuteGMPanel.hide() 开始执行 ===");

         try
         {
            var panel:CuteGMPanel = getInstance();
            trace("面板当前可见状态: " + panel.isVisible);
            trace("面板父容器是否存在: " + (panel.parent != null));
            TiaoShi.txtShow("面板当前可见状态: " + panel.isVisible);
            TiaoShi.txtShow("面板父容器是否存在: " + (panel.parent != null));

            if(panel.isVisible && panel.parent)
            {
               trace("准备从舞台移除面板");
               TiaoShi.txtShow("准备从舞台移除面板");

               panel.parent.removeChild(panel);
               panel.isVisible = false;

               trace("GM面板隐藏成功！");
               TiaoShi.txtShow("GM面板隐藏成功！");
            }
            else
            {
               trace("面板已经隐藏或不在舞台上");
               TiaoShi.txtShow("面板已经隐藏或不在舞台上");
            }
         }
         catch(error:Error)
         {
            trace("隐藏GM面板时出错: " + error.message);
            TiaoShi.txtShow("隐藏GM面板时出错: " + error.message);
         }
      }
      
      public static function toggle() : void
      {
         trace("=== CuteGMPanel.toggle() 开始执行 ===");
         TiaoShi.txtShow("=== CuteGMPanel.toggle() 开始执行 ===");

         try
         {
            var panel:CuteGMPanel = getInstance();
            trace("获取面板实例成功，当前可见状态: " + panel.isVisible);
            TiaoShi.txtShow("获取面板实例成功，当前可见状态: " + panel.isVisible);

            if(panel.isVisible)
            {
               trace("面板当前可见，准备隐藏");
               TiaoShi.txtShow("面板当前可见，准备隐藏");
               hide();
            }
            else
            {
               trace("面板当前不可见，准备显示");
               TiaoShi.txtShow("面板当前不可见，准备显示");
               show();
            }

            trace("=== CuteGMPanel.toggle() 执行完成 ===");
            TiaoShi.txtShow("=== CuteGMPanel.toggle() 执行完成 ===");
         }
         catch(error:Error)
         {
            trace("CuteGMPanel.toggle() 执行出错: " + error.message);
            trace("错误堆栈: " + error.getStackTrace());
            TiaoShi.txtShow("CuteGMPanel.toggle() 执行出错: " + error.message);
         }
      }
      
      private function initializePanel() : void
      {
         this.createMainPanel();
         this.createTitleBar();
         this.createTabContainer();
         this.createContentContainer();
         this.createStatusBar();
         this.setupEventListeners();
      }
      
      private function createMainPanel() : void
      {
         mainPanel = new Sprite();
         var bg:Graphics = mainPanel.graphics;
         bg.clear();
         var shadow:DropShadowFilter = new DropShadowFilter(8,45,0,0.3,8,8,1,3);
         mainPanel.filters = [shadow];
         var matrix:Matrix = new Matrix();
         matrix.createGradientBox(PANEL_WIDTH,PANEL_HEIGHT,Math.PI / 2,0,0);
         bg.beginGradientFill(GradientType.LINEAR,[WHITE,PURPLE_LIGHT],[1,1],[0,255],matrix);
         bg.lineStyle(2,PINK_DARK,1);
         bg.drawRoundRect(0,0,PANEL_WIDTH,PANEL_HEIGHT,20,20);
         bg.endFill();
         mainPanel.x = (Main._stage.stageWidth - PANEL_WIDTH) / 2;
         mainPanel.y = (Main._stage.stageHeight - PANEL_HEIGHT) / 2;
         addChild(mainPanel);
      }
      
      private function createTitleBar() : void
      {
         titleBar = new Sprite();
         var bg:Graphics = titleBar.graphics;
         var matrix:Matrix = new Matrix();
         matrix.createGradientBox(PANEL_WIDTH,40,Math.PI / 2,0,0);
         bg.beginGradientFill(GradientType.LINEAR,[PINK_MEDIUM,PINK_LIGHT],[1,1],[0,255],matrix);
         bg.drawRoundRect(0,0,PANEL_WIDTH,40,20,20);
         bg.endFill();
         var titleText:TextField = createTextField("🌸 可爱GM助手 🌸",20,WHITE,true);
         titleText.x = 20;
         titleText.y = 10;
         titleBar.addChild(titleText);
         var closeBtn:Sprite = createCuteButton("✕",30,30,PINK_DARK);
         closeBtn.x = PANEL_WIDTH - 40;
         closeBtn.y = 5;
         closeBtn.addEventListener(MouseEvent.CLICK,onCloseClick);
         titleBar.addChild(closeBtn);
         var minBtn:Sprite = createCuteButton("－",30,30,PINK_MEDIUM);
         minBtn.x = PANEL_WIDTH - 75;
         minBtn.y = 5;
         minBtn.addEventListener(MouseEvent.CLICK,onMinimizeClick);
         titleBar.addChild(minBtn);
         mainPanel.addChild(titleBar);
      }
      
      private function createCuteButton(text:String, width:Number, height:Number, color:uint) : Sprite
      {
         var btn:Sprite = new Sprite();
         var bg:Graphics = btn.graphics;
         bg.beginFill(color,0.8);
         bg.lineStyle(1,WHITE,0.8);
         bg.drawRoundRect(0,0,width,height,8,8);
         bg.endFill();
         var btnText:TextField = createTextField(text,14,WHITE,true);
         btnText.x = (width - btnText.textWidth) / 2;
         btnText.y = (height - btnText.textHeight) / 2;
         btn.addChild(btnText);
         btn.buttonMode = true;
         btn.useHandCursor = true;
         btn.addEventListener(MouseEvent.MOUSE_OVER,onButtonOver);
         btn.addEventListener(MouseEvent.MOUSE_OUT,onButtonOut);
         return btn;
      }
      
      private function createTextField(text:String, size:Number, color:uint, bold:Boolean = false) : TextField
      {
         var tf:TextField = new TextField();
         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = size;
         format.color = color;
         format.bold = bold;
         tf.defaultTextFormat = format;
         tf.text = text;
         tf.autoSize = TextFieldAutoSize.LEFT;
         tf.selectable = false;
         tf.mouseEnabled = false;
         return tf;
      }
      
      private function onButtonOver(e:MouseEvent) : void
      {
         var btn:Sprite = e.target as Sprite;
         btn.scaleY = 1.1;
         btn.scaleX = 1.1;
         btn.alpha = 1;
      }
      
      private function onButtonOut(e:MouseEvent) : void
      {
         var btn:Sprite = e.target as Sprite;
         btn.scaleY = 1;
         btn.scaleX = 1;
         btn.alpha = 0.8;
      }
      
      private function onCloseClick(e:MouseEvent) : void
      {
         hide();
      }
      
      private function onMinimizeClick(e:MouseEvent) : void
      {
         hide();
      }
      
      private function playShowAnimation() : void
      {
         this.alpha = 0;
         this.scaleY = 0.8;
         this.scaleX = 0.8;
         addEventListener(Event.ENTER_FRAME,onShowAnimation);
      }
      
      private function onShowAnimation(e:Event) : void
      {
         this.alpha += 0.1;
         this.scaleX = this.scaleY = this.scaleY + 0.02;
         if(this.alpha >= 1)
         {
            this.alpha = 1;
            this.scaleX = this.scaleY = 1;
            removeEventListener(Event.ENTER_FRAME,onShowAnimation);
         }
      }
      
      private function setupEventListeners() : void
      {
         titleBar.addEventListener(MouseEvent.MOUSE_DOWN,onTitleMouseDown);
         Main._stage.addEventListener(MouseEvent.MOUSE_UP,onStageMouseUp);
         Main._stage.addEventListener(MouseEvent.MOUSE_MOVE,onStageMouseMove);
      }
      
      private function onTitleMouseDown(e:MouseEvent) : void
      {
         isDragging = true;
         dragOffset = new Point(e.stageX - mainPanel.x,e.stageY - mainPanel.y);
      }
      
      private function onStageMouseUp(e:MouseEvent) : void
      {
         isDragging = false;
      }
      
      private function onStageMouseMove(e:MouseEvent) : void
      {
         if(isDragging)
         {
            mainPanel.x = e.stageX - dragOffset.x;
            mainPanel.y = e.stageY - dragOffset.y;
         }
      }
      
      private function createTabContainer() : void
      {
         tabContainer = new Sprite();
         tabContainer.y = 40;
         var i:int = 0;
         while(i < tabData.length)
         {
            var tab:Sprite = createTab(tabData[i].name,i);
            tab.x = i * 130;
            tabContainer.addChild(tab);
            tabs.push(tab);
            i++;
         }
         setActiveTab(0);
         mainPanel.addChild(tabContainer);
      }
      
      private function createTab(name:String, index:int) : Sprite
      {
         var tab:Sprite = new Sprite();
         var bg:Graphics = tab.graphics;
         bg.beginFill(PINK_LIGHT,0.6);
         bg.lineStyle(1,PINK_DARK,0.8);
         bg.drawRoundRect(0,0,125,TAB_HEIGHT,10,10);
         bg.endFill();
         var tabText:TextField = createTextField(name,14,PINK_DARK,true);
         tabText.x = (125 - tabText.textWidth) / 2;
         tabText.y = (TAB_HEIGHT - tabText.textHeight) / 2;
         tab.addChild(tabText);
         tab.buttonMode = true;
         tab.useHandCursor = true;
         tab.name = "tab_" + index;
         tab.addEventListener(MouseEvent.CLICK,onTabClick);
         tab.addEventListener(MouseEvent.MOUSE_OVER,onTabOver);
         tab.addEventListener(MouseEvent.MOUSE_OUT,onTabOut);
         return tab;
      }
      
      private function onTabClick(e:MouseEvent) : void
      {
         var tab:Sprite = e.target as Sprite;
         var index:int = int(parseInt(tab.name.split("_")[1]));
         setActiveTab(index);
      }
      
      private function onTabOver(e:MouseEvent) : void
      {
         var tab:Sprite = e.target as Sprite;
         var index:int = int(parseInt(tab.name.split("_")[1]));
         if(index != currentTab)
         {
            tab.alpha = 0.8;
         }
      }
      
      private function onTabOut(e:MouseEvent) : void
      {
         var tab:Sprite = e.target as Sprite;
         var index:int = int(parseInt(tab.name.split("_")[1]));
         if(index != currentTab)
         {
            tab.alpha = 0.6;
         }
      }
      
      private function setActiveTab(index:int) : void
      {
         var i:int = 0;
         while(i < tabs.length)
         {
            var tab:Sprite = tabs[i];
            var bg:Graphics = tab.graphics;
            bg.clear();
            if(i == index)
            {
               bg.beginFill(PINK_MEDIUM,1);
               bg.lineStyle(2,PINK_DARK,1);
               tab.alpha = 1;
            }
            else
            {
               bg.beginFill(PINK_LIGHT,0.6);
               bg.lineStyle(1,PINK_DARK,0.8);
               tab.alpha = 0.6;
            }
            bg.drawRoundRect(0,0,125,TAB_HEIGHT,10,10);
            bg.endFill();
            i++;
         }
         currentTab = index;
         showTabContent(index);
      }
      
      private function createContentContainer() : void
      {
         contentContainer = new Sprite();
         contentContainer.y = 40 + TAB_HEIGHT;
         var bg:Graphics = contentContainer.graphics;
         bg.beginFill(WHITE,0.9);
         bg.lineStyle(1,PINK_LIGHT,0.8);
         bg.drawRoundRect(10,0,PANEL_WIDTH - 20,CONTENT_HEIGHT,10,10);
         bg.endFill();
         this.createAllTabContents();
         mainPanel.addChild(contentContainer);
      }
      
      private function createStatusBar() : void
      {
         statusBar = new Sprite();
         statusBar.y = PANEL_HEIGHT - 30;
         var bg:Graphics = statusBar.graphics;
         bg.beginFill(PURPLE_LIGHT,0.8);
         bg.drawRoundRect(0,0,PANEL_WIDTH,30,0,0);
         bg.endFill();
         var statusText:TextField = createTextField("就绪 | 点击P1按钮打开/关闭GM界面",12,PINK_DARK);
         statusText.x = 20;
         statusText.y = 8;
         statusBar.addChild(statusText);
         mainPanel.addChild(statusBar);
      }
      
      private function createAllTabContents() : void
      {
         tabContents = [];
         var i:int = 0;
         while(i < 6)
         {
            var content:Sprite = new Sprite();
            content.x = 15;
            content.y = 10;
            content.visible = false;
            switch(i)
            {
               case 0:
                  this.createCharacterContent(content);
                  break;
               case 1:
                  this.createEquipmentContent(content);
                  break;
               case 2:
                  this.createPetContent(content);
                  break;
               case 3:
                  this.createItemContent(content);
                  break;
               case 4:
                  this.createQuickContent(content);
                  break;
               case 5:
                  this.createToolContent(content);
                  break;
            }
            tabContents.push(content);
            contentContainer.addChild(content);
            i++;
         }
         showTabContent(0);
      }
      
      private function showTabContent(index:int) : void
      {
         var i:int = 0;
         while(i < tabContents.length)
         {
            tabContents[i].visible = i == index;
            i++;
         }
      }
      
      private function createCharacterContent(container:Sprite) : void
      {
         var basicGroup:Sprite;
         var levelInput:TextField;
         var levelBtn:Sprite;
         var goldInput:TextField;
         var goldBtn:Sprite;
         var skillInput:TextField;
         var skillBtn:Sprite;
         var killInput:TextField;
         var killBtn:Sprite;
         var quickGroup:Sprite;
         var unlockBtns:Array;
         var i:int;
         var btn:Sprite;
         var achieveGroup:Sprite;
         var achieveBtns:Array;
         var j:int;
         var achieveBtn:Sprite;
         var yPos:Number = 10;
         var title:TextField = createTextField("🎮 角色属性管理",18,PINK_DARK,true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;
         basicGroup = createGroup("基础属性",350,200);
         basicGroup.x = 10;
         basicGroup.y = yPos;
         container.addChild(basicGroup);
         levelInput = createInputField("99",80);
         levelInput.x = 20;
         levelInput.y = 30;
         basicGroup.addChild(levelInput);
         levelBtn = createCuteButton("设置等级",80,25,PINK_MEDIUM);
         levelBtn.x = 110;
         levelBtn.y = 30;
         levelBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            setPlayerLevel(parseInt(levelInput.text));
         });
         basicGroup.addChild(levelBtn);
         goldInput = createInputField("999999999",80);
         goldInput.x = 20;
         goldInput.y = 65;
         basicGroup.addChild(goldInput);
         goldBtn = createCuteButton("设置金币",80,25,PINK_MEDIUM);
         goldBtn.x = 110;
         goldBtn.y = 65;
         goldBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            setPlayerGold(parseInt(goldInput.text));
         });
         basicGroup.addChild(goldBtn);
         skillInput = createInputField("999",80);
         skillInput.x = 20;
         skillInput.y = 100;
         basicGroup.addChild(skillInput);
         skillBtn = createCuteButton("设置技能点",80,25,PINK_MEDIUM);
         skillBtn.x = 110;
         skillBtn.y = 100;
         skillBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            setPlayerSkillPoints(parseInt(skillInput.text));
         });
         basicGroup.addChild(skillBtn);
         killInput = createInputField("999999",80);
         killInput.x = 20;
         killInput.y = 135;
         basicGroup.addChild(killInput);
         killBtn = createCuteButton("设置击杀点",80,25,PINK_MEDIUM);
         killBtn.x = 110;
         killBtn.y = 135;
         killBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            setPlayerKillPoints(parseInt(killInput.text));
         });
         basicGroup.addChild(killBtn);
         quickGroup = createGroup("快捷功能",350,200);
         quickGroup.x = 380;
         quickGroup.y = yPos;
         container.addChild(quickGroup);
         unlockBtns = [{
            "text":"背包解锁",
            "func":unlockBag
         },{
            "text":"宠栏解锁",
            "func":unlockPetSlot
         },{
            "text":"精灵槽解锁",
            "func":unlockElvesSlot
         },{
            "text":"特殊栏解锁",
            "func":unlockSpecialSlot
         },{
            "text":"关卡解锁",
            "func":unlockStages
         },{
            "text":"图鉴添加",
            "func":addAllCards
         }];
         i = 0;
         while(i < unlockBtns.length)
         {
            btn = createCuteButton(unlockBtns[i].text,100,25,PINK_MEDIUM);
            btn.x = 20 + i % 2 * 110;
            btn.y = 30 + Math.floor(i / 2) * 35;
            btn.addEventListener(MouseEvent.CLICK,unlockBtns[i].func);
            quickGroup.addChild(btn);
            i++;
         }
         yPos += 220;
         achieveGroup = createGroup("成就与其他",720,100);
         achieveGroup.x = 10;
         achieveGroup.y = yPos;
         container.addChild(achieveGroup);
         achieveBtns = [{
            "text":"成就全亮",
            "func":unlockAllAchievements
         },{
            "text":"过检测",
            "func":bypassDetection
         },{
            "text":"宝珠满级",
            "func":maxBaoZhu
         },{
            "text":"悬赏全满",
            "func":maxWanted
         },{
            "text":"四职业技能",
            "func":allClassSkills
         }];
         j = 0;
         while(j < achieveBtns.length)
         {
            achieveBtn = createCuteButton(achieveBtns[j].text,100,25,PINK_DARK);
            achieveBtn.x = 20 + j * 110;
            achieveBtn.y = 30;
            achieveBtn.addEventListener(MouseEvent.CLICK,achieveBtns[j].func);
            achieveGroup.addChild(achieveBtn);
            j++;
         }
      }
      
      private function createEquipmentContent(container:Sprite) : void
      {
         var equipGroup:Sprite;
         var equipIdInput:TextField;
         var equipCountInput:TextField;
         var addEquipBtn:Sprite;
         var oneKeyEquipBtn:Sprite;
         var blessGroup:Sprite;
         var blessBtns:Array;
         var i:int;
         var blessBtn:Sprite;
         var customGroup:Sprite;
         var customText:TextField;
         var customBtn:Sprite;
         var yPos:Number = 10;
         var title:TextField = createTextField("⚔️ 装备管理",18,PINK_DARK,true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;
         equipGroup = createGroup("装备添加",350,150);
         equipGroup.x = 10;
         equipGroup.y = yPos;
         container.addChild(equipGroup);
         equipIdInput = createInputField("装备ID",100);
         equipIdInput.x = 20;
         equipIdInput.y = 30;
         equipGroup.addChild(equipIdInput);
         equipCountInput = createInputField("1",60);
         equipCountInput.x = 130;
         equipCountInput.y = 30;
         equipGroup.addChild(equipCountInput);
         addEquipBtn = createCuteButton("添加装备",80,25,PINK_MEDIUM);
         addEquipBtn.x = 200;
         addEquipBtn.y = 30;
         addEquipBtn.addEventListener(MouseEvent.CLICK,function(e:MouseEvent):void
         {
            addEquipment(parseInt(equipIdInput.text),parseInt(equipCountInput.text));
         });
         equipGroup.addChild(addEquipBtn);
         oneKeyEquipBtn = createCuteButton("一键装备",120,30,PINK_DARK);
         oneKeyEquipBtn.x = 20;
         oneKeyEquipBtn.y = 70;
         oneKeyEquipBtn.addEventListener(MouseEvent.CLICK,oneKeyEquipment);
         equipGroup.addChild(oneKeyEquipBtn);
         blessGroup = createGroup("祝福系统",350,150);
         blessGroup.x = 380;
         blessGroup.y = yPos;
         container.addChild(blessGroup);
         blessBtns = ["祝福界面1","祝福界面2","祝福界面3","祝福界面4","祝福界面5","祝福界面6","星灵王祝福"];
         i = 0;
         while(i < blessBtns.length)
         {
            blessBtn = createCuteButton(blessBtns[i],80,20,PINK_LIGHT);
            blessBtn.x = 20 + i % 3 * 85;
            blessBtn.y = 30 + Math.floor(i / 3) * 25;
            blessBtn.name = "bless_" + (i + 1);
            blessBtn.addEventListener(MouseEvent.CLICK,onBlessClick);
            blessGroup.addChild(blessBtn);
            i++;
         }
         yPos += 170;
         customGroup = createGroup("自定义装备",720,200);
         customGroup.x = 10;
         customGroup.y = yPos;
         container.addChild(customGroup);
         customText = createTextField("点击下方按钮打开自定义装备界面",14,PINK_DARK);
         customText.x = 20;
         customText.y = 30;
         customGroup.addChild(customText);
         customBtn = createCuteButton("打开自定义装备",150,30,PINK_DARK);
         customBtn.x = 20;
         customBtn.y = 60;
         customBtn.addEventListener(MouseEvent.CLICK,openCustomEquipment);
         customGroup.addChild(customBtn);
      }
      
      private function createGroup(title:String, width:Number, height:Number) : Sprite
      {
         var group:Sprite = new Sprite();
         var bg:Graphics = group.graphics;
         bg.beginFill(PURPLE_LIGHT,0.3);
         bg.lineStyle(1,PINK_MEDIUM,0.8);
         bg.drawRoundRect(0,0,width,height,8,8);
         bg.endFill();
         var titleText:TextField = createTextField(title,14,PINK_DARK,true);
         titleText.x = 10;
         titleText.y = 5;
         group.addChild(titleText);
         return group;
      }
      
      private function createInputField(defaultText:String, width:Number) : TextField
      {
         var input:TextField = new TextField();
         input.width = width;
         input.height = 25;
         input.background = true;
         input.backgroundColor = WHITE;
         input.border = true;
         input.borderColor = PINK_MEDIUM;
         input.type = TextFieldType.INPUT;
         input.textColor = 3355443;
         input.text = defaultText;
         var format:TextFormat = new TextFormat();
         format.font = "Arial";
         format.size = 12;
         input.defaultTextFormat = format;
         return input;
      }
      
      private function showSuccessMessage(message:String) : void
      {
         NewMC.Open("文字提示",Main._stage,400,400,30,0,true,2,"✨ " + message + " ✨");
         updateStatusBar("操作成功: " + message);
      }
      
      private function updateStatusBar(message:String) : void
      {
         if(statusBar && statusBar.numChildren > 0)
         {
            var statusText:TextField = statusBar.getChildAt(0) as TextField;
            if(statusText)
            {
               statusText.text = message;
               statusText.alpha = 0.5;
               statusText.addEventListener(Event.ENTER_FRAME,onStatusFlash);
            }
         }
      }
      
      private function onStatusFlash(e:Event) : void
      {
         var statusText:TextField = e.target as TextField;
         statusText.alpha += 0.1;
         if(statusText.alpha >= 1)
         {
            statusText.alpha = 1;
            statusText.removeEventListener(Event.ENTER_FRAME,onStatusFlash);
            setTimeout(function():void
            {
               if(statusText)
               {
                  statusText.text = "就绪 | 点击P1按钮打开/关闭GM界面";
               }
            },3000);
         }
      }
      
      private function setTimeout(func:Function, delay:Number) : void
      {
         var timer:Timer = new Timer(delay,1);
         timer.addEventListener(TimerEvent.TIMER,function(e:TimerEvent):void
         {
            func();
            timer.removeEventListener(TimerEvent.TIMER,arguments.callee);
         });
         timer.start();
      }
      
      private function setPlayerLevel(level:int) : void
      {
         Main.player1.level = VT.createVT(level);
         if(Main.player2)
         {
            Main.player2.level = VT.createVT(level);
         }
         showSuccessMessage("等级设置成功：" + level);
      }
      
      private function setPlayerGold(gold:int) : void
      {
         Main.player1.gold.setValue(gold);
         if(Main.player2)
         {
            Main.player2.gold.setValue(gold);
         }
         showSuccessMessage("金币设置成功：" + gold);
      }
      
      private function setPlayerSkillPoints(points:int) : void
      {
         Main.player_1.data.points.setValue(points);
         if(Main.player_2)
         {
            Main.player_2.data.points.setValue(points);
         }
         showSuccessMessage("技能点设置成功：" + points);
      }
      
      private function setPlayerKillPoints(points:int) : void
      {
         Main.player1.killPoint.setValue(points);
         if(Main.player2)
         {
            Main.player2.killPoint.setValue(points);
         }
         showSuccessMessage("击杀点设置成功：" + points);
      }
      
      private function unlockBag(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 50)
         {
            Main.player1.getBag().bagOpen[i] = true;
            i++;
         }
         showSuccessMessage("背包已全部解锁！");
      }
      
      private function unlockPetSlot(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 50)
         {
            Main.player1.getPetSlot().slotOpen[i] = true;
            i++;
         }
         showSuccessMessage("宠物栏已全部解锁！");
      }
      
      private function unlockElvesSlot(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 50)
         {
            Main.player1.getElvesSlot().slotOpen[i] = true;
            i++;
         }
         showSuccessMessage("精灵槽已全部解锁！");
      }
      
      private function unlockSpecialSlot(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 50)
         {
            Main.player1.getSpecialSlot().slotOpen[i] = true;
            i++;
         }
         showSuccessMessage("特殊栏已全部解锁！");
      }
      
      private function unlockStages(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 150)
         {
            Main.guanKa[i] = 3;
            i++;
         }
         showSuccessMessage("所有关卡已解锁！");
      }
      
      private function addAllCards(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 12000)
         {
            CardPanel.monsterSlot.addMonsterSlot(i);
            i++;
         }
         showSuccessMessage("图鉴添加成功！");
      }
      
      private function unlockAllAchievements(e:MouseEvent) : void
      {
         var myXml:XML = XMLAsset.createXML(Data2.AchNum);
         for each(var property in myXml.成就)
         {
            var id:Number = Number(property.编号);
            var name:String = String(property.名字);
            var frame:Number = Number(property.帧数);
            var sm:String = String(property.说明);
            var everyDady:Boolean = (property.是否每日.toString() == "true") as Boolean;
            var numType:Number = Number(property.类型);
            var rewardAc:Number = Number(property.奖励成就点);
            var goodsId:String = String(property.指定id);
            var ry:Boolean = (property.是否同时.toString() == "true") as Boolean;
            var goodsType:String = String(property.id类型);
            var nType:Number = Number(property.获取方式);
            var data:AchNumBasicData = AchNumBasicData.ceartAchNum(id,name,frame,sm,everyDady,numType,rewardAc,goodsId,goodsType,2,-1,-1,ry,nType);
            AchNumFactory.allData.push(data);
         }
         showSuccessMessage("所有成就已点亮！");
      }
      
      private function bypassDetection(e:MouseEvent) : void
      {
         showSuccessMessage("检测已绕过！");
      }
      
      private function maxBaoZhu(e:MouseEvent) : void
      {
         Panel_youling.lvArr = [100,100,100,100,100];
         Panel_youling.bzNumArr = [2147483647,2147483647,2147483647,2147483647,2147483647];
         showSuccessMessage("宝珠已满级！");
      }
      
      private function maxWanted(e:MouseEvent) : void
      {
         showSuccessMessage("悬赏已全满！");
      }
      
      private function allClassSkills(e:MouseEvent) : void
      {
         showSuccessMessage("四职业技能已获得！");
      }
      
      private function addEquipment(equipId:int, count:int) : void
      {
         var i:int = 0;
         while(i < count)
         {
            Main.player_1.data.getBag().addEquipBag(EquipFactory.createEquipByID(equipId));
            i++;
         }
         showSuccessMessage("装备添加成功：ID " + equipId + " x" + count);
      }
      
      private function oneKeyEquipment(e:MouseEvent) : void
      {
         var gm:GM = new GM();
         gm.一键装备(null);
         showSuccessMessage("一键装备完成！");
      }
      
      private function onBlessClick(e:MouseEvent) : void
      {
         var btn:Sprite = e.target as Sprite;
         var index:int = int(parseInt(btn.name.split("_")[1]));
         switch(index)
         {
            case 1:
               JinHuaPanel.open(true,1,4);
               break;
            case 2:
               JinHuaPanel.open(true,2,3);
               break;
            case 3:
               JinHuaPanel.open(true,3,1);
               break;
            case 4:
               JinHuaPanel.open(true,4,2);
               break;
            case 5:
               JinHuaPanel.open(true,5,5);
               break;
            case 6:
               JinHuaPanel.open(true,6,8);
               break;
            case 7:
               JinHuaPanel2.open(true);
         }
         showSuccessMessage("祝福界面已打开");
      }
      
      private function openCustomEquipment(e:MouseEvent) : void
      {
         showSuccessMessage("自定义装备界面功能开发中...");
      }
      
      private function createPetContent(container:Sprite) : void
      {
         var yPos:Number = 10;
         var title:TextField = createTextField("🐾 宠物管理",18,PINK_DARK,true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;
         var oneKeyPetBtn:Sprite = createCuteButton("🐾 一键宠物",150,40,PINK_DARK);
         oneKeyPetBtn.x = 50;
         oneKeyPetBtn.y = yPos;
         oneKeyPetBtn.addEventListener(MouseEvent.CLICK,oneKeyPets);
         container.addChild(oneKeyPetBtn);
         var petEquipBtn:Sprite = createCuteButton("🎒 一键宠物装备",150,40,PINK_MEDIUM);
         petEquipBtn.x = 220;
         petEquipBtn.y = yPos;
         petEquipBtn.addEventListener(MouseEvent.CLICK,oneKeyPetEquip);
         container.addChild(petEquipBtn);
         yPos += 60;
         var clearPetBtn:Sprite = createCuteButton("清空宠物",120,30,16739179);
         clearPetBtn.x = 50;
         clearPetBtn.y = yPos;
         clearPetBtn.addEventListener(MouseEvent.CLICK,clearPets);
         container.addChild(clearPetBtn);
      }
      
      private function createItemContent(container:Sprite) : void
      {
         var yPos:Number = 10;
         var title:TextField = createTextField("💎 道具管理",18,PINK_DARK,true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;
         var itemBtns:Array = [{
            "text":"💎 一键道具",
            "func":oneKeyItems
         },{
            "text":"🧪 一键消耗品",
            "func":oneKeySupplies
         },{
            "text":"💍 一键技能石",
            "func":oneKeySkillStones
         },{
            "text":"🏆 一键称号",
            "func":oneKeyTitles
         },{
            "text":"✨ 一键精灵",
            "func":oneKeyElves
         }];
         var i:int = 0;
         while(i < itemBtns.length)
         {
            var btn:Sprite = createCuteButton(itemBtns[i].text,140,35,PINK_MEDIUM);
            btn.x = 50 + i % 3 * 160;
            btn.y = yPos + Math.floor(i / 3) * 50;
            btn.addEventListener(MouseEvent.CLICK,itemBtns[i].func);
            container.addChild(btn);
            i++;
         }
         yPos += 120;
         var clearBtns:Array = [{
            "text":"清空背包",
            "func":clearBag
         },{
            "text":"清空仓库",
            "func":clearStorage
         },{
            "text":"清空精灵",
            "func":clearElves
         },{
            "text":"清空称号",
            "func":clearTitles
         }];
         var j:int = 0;
         while(j < clearBtns.length)
         {
            var clearBtn:Sprite = createCuteButton(clearBtns[j].text,120,30,16739179);
            clearBtn.x = 50 + j * 140;
            clearBtn.y = yPos;
            clearBtn.addEventListener(MouseEvent.CLICK,clearBtns[j].func);
            container.addChild(clearBtn);
            j++;
         }
      }
      
      private function createQuickContent(container:Sprite) : void
      {
         var yPos:Number = 10;
         var title:TextField = createTextField("🎯 一键功能",18,PINK_DARK,true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;
         var quickBtns:Array = [{
            "text":"🎮 一键角色满级",
            "func":quickMaxCharacter,
            "color":PINK_DARK
         },{
            "text":"⚔️ 一键装备",
            "func":oneKeyEquipment,
            "color":PINK_DARK
         },{
            "text":"🐾 一键宠物",
            "func":oneKeyPets,
            "color":PINK_DARK
         },{
            "text":"💎 一键道具",
            "func":oneKeyItems,
            "color":PINK_DARK
         },{
            "text":"🏆 一键成就",
            "func":unlockAllAchievements,
            "color":PINK_DARK
         },{
            "text":"🔓 一键解锁",
            "func":quickUnlockAll,
            "color":PINK_DARK
         }];
         var i:int = 0;
         while(i < quickBtns.length)
         {
            var btn:Sprite = createCuteButton(quickBtns[i].text,200,40,quickBtns[i].color);
            btn.x = 50 + i % 3 * 220;
            btn.y = yPos + Math.floor(i / 3) * 60;
            btn.addEventListener(MouseEvent.CLICK,quickBtns[i].func);
            container.addChild(btn);
            i++;
         }
      }
      
      private function createToolContent(container:Sprite) : void
      {
         var yPos:Number = 10;
         var title:TextField = createTextField("🔧 工具箱",18,PINK_DARK,true);
         title.x = 10;
         title.y = yPos;
         container.addChild(title);
         yPos += 35;
         var codeBtns:Array = ["装备代码","道具代码","宠物代码","精灵代码","称号代码","宝石代码","药品代码","宠装代码"];
         var i:int = 0;
         while(i < codeBtns.length)
         {
            var btn:Sprite = createCuteButton(codeBtns[i],120,30,PINK_LIGHT);
            btn.x = 50 + i % 4 * 140;
            btn.y = yPos + Math.floor(i / 4) * 40;
            btn.name = "code_" + i;
            btn.addEventListener(MouseEvent.CLICK,onCodeQuery);
            container.addChild(btn);
            i++;
         }
         yPos += 100;
         var codeDisplay:TextField = new TextField();
         codeDisplay.x = 50;
         codeDisplay.y = yPos;
         codeDisplay.width = 650;
         codeDisplay.height = 200;
         codeDisplay.background = true;
         codeDisplay.backgroundColor = WHITE;
         codeDisplay.border = true;
         codeDisplay.borderColor = PINK_MEDIUM;
         codeDisplay.multiline = true;
         codeDisplay.wordWrap = true;
         codeDisplay.text = "点击上方按钮查询相应代码...";
         codeDisplay.name = "codeDisplay";
         container.addChild(codeDisplay);
      }
      
      private function oneKeyPets(e:MouseEvent) : void
      {
         var gm:GM = new GM();
         gm.一键宠物(null);
         showSuccessMessage("一键宠物完成！");
      }
      
      private function oneKeyPetEquip(e:MouseEvent) : void
      {
         var gm:GM = new GM();
         gm.一键宠物装备(null);
         showSuccessMessage("一键宠物装备完成！");
      }
      
      private function clearPets(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 50)
         {
            Main.player1.getPetSlot().slot[i] = null;
            i++;
         }
         showSuccessMessage("所有宠物已清除！");
      }
      
      private function oneKeyItems(e:MouseEvent) : void
      {
         var gm:GM = new GM();
         gm.一键道具(null);
         showSuccessMessage("一键道具完成！");
      }
      
      private function oneKeySupplies(e:MouseEvent) : void
      {
         var gm:GM = new GM();
         gm.一键消耗品(null);
         showSuccessMessage("一键消耗品完成！");
      }
      
      private function oneKeySkillStones(e:MouseEvent) : void
      {
         var gm:GM = new GM();
         gm.一键技能石(null);
         showSuccessMessage("一键技能石完成！");
      }
      
      private function oneKeyTitles(e:MouseEvent) : void
      {
         var gm:GM = new GM();
         gm.一键称号(null);
         showSuccessMessage("一键称号完成！");
      }
      
      private function oneKeyElves(e:MouseEvent) : void
      {
         var gm:GM = new GM();
         gm.一键精灵(null);
         showSuccessMessage("一键精灵完成！");
      }
      
      private function clearBag(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 35)
         {
            Main.player1.getBag().equipBag[i] = null;
            Main.player1.getBag().suppliesBag[i] = null;
            Main.player_1.data.getBag().gemBag[i] = null;
            Main.player1.getBag().otherobjBag[i] = null;
            Main.player_1.data.getBag().questBag[i] = null;
            i++;
         }
         showSuccessMessage("背包已清空！");
      }
      
      private function clearStorage(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 35)
         {
            StoragePanel.storage.equipStorage[i] = null;
            StoragePanel.storage.suppliesStorage[i] = null;
            StoragePanel.storage.gemStorage[i] = null;
            StoragePanel.storage.otherobjStorage[i] = null;
            i++;
         }
         showSuccessMessage("仓库已清空！");
      }
      
      private function clearElves(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 50)
         {
            Main.player1.getElvesSlot().slot[i] = null;
            i++;
         }
         showSuccessMessage("所有精灵已清除！");
      }
      
      private function clearTitles(e:MouseEvent) : void
      {
         var i:int = 0;
         while(i < 50)
         {
            Main.player1.getTitleSlot().slot[i] = null;
            i++;
         }
         showSuccessMessage("所有称号已清除！");
      }
      
      private function quickMaxCharacter(e:MouseEvent) : void
      {
         setPlayerLevel(99);
         setPlayerGold(999999999);
         setPlayerSkillPoints(999);
         setPlayerKillPoints(999999);
         showSuccessMessage("角色属性已满级！");
      }
      
      private function quickUnlockAll(e:MouseEvent) : void
      {
         unlockBag(null);
         unlockPetSlot(null);
         unlockElvesSlot(null);
         unlockSpecialSlot(null);
         unlockStages(null);
         showSuccessMessage("所有功能已解锁！");
      }
      
      private function onCodeQuery(e:MouseEvent) : void
      {
         var btn:Sprite = e.target as Sprite;
         var index:int = int(parseInt(btn.name.split("_")[1]));
         var display:TextField = btn.parent.getChildByName("codeDisplay") as TextField;
         var gm:GM = new GM();
         gm.yj14 = display;
         switch(index)
         {
            case 0:
               gm.装备代码(null);
               break;
            case 1:
               gm.道具代码(null);
               break;
            case 2:
               gm.宠物代码(null);
               break;
            case 3:
               gm.精灵代码(null);
               break;
            case 4:
               gm.称号代码(null);
               break;
            case 5:
               gm.宝石代码(null);
               break;
            case 6:
               gm.药品代码(null);
               break;
            case 7:
               gm.宠装代码(null);
         }
      }
   }
}

